<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $action = $_GET['action'] ?? 'daily';
    
    if ($action === 'daily') {
        // 获取每日推荐
        $apiUrl = 'http://homepages.kuwo.cn/mgxh.s?type=tuijian11&apiv=17&dailyrec=1&notrace=0';
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10
            ]
        ]);
        
        $content = @file_get_contents($apiUrl, false, $context);
        
        if ($content === false) {
            throw new Exception('无法获取API数据');
        }
        
        // 快速解析 - 只提取基本信息
        preg_match_all('/<music[^>]*name="([^"]*)"[^>]*artist="([^"]*)"[^>]*id="([^"]*)"[^>]*img="([^"]*)"[^>]*album="([^"]*)"[^>]*duration="([^"]*)"[^>]*>/', $content, $matches, PREG_SET_ORDER);
        
        $songs = [];
        foreach ($matches as $match) {
            $songs[] = [
                'name' => html_entity_decode($match[1]),
                'artist' => html_entity_decode($match[2]),
                'rid' => $match[3],
                'img' => $match[4],
                'album' => html_entity_decode($match[5]),
                'duration' => $match[6],
                'duration_formatted' => sprintf('%d:%02d', floor($match[6] / 60), $match[6] % 60)
            ];
        }
        
        echo json_encode([
            'code' => 200,
            'success' => true,
            'message' => '获取成功',
            'data' => [
                'total' => count($songs),
                'songs' => $songs
            ],
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
        
    } elseif ($action === 'similar') {
        // 获取相似歌曲
        $rid = isset($_GET['id']) ? intval($_GET['id']) : 0;
        $num = isset($_GET['num']) ? intval($_GET['num']) : 3;
        
        if ($rid <= 0) {
            throw new Exception('歌曲ID参数无效');
        }
        
        $baseUrl = 'http://wapi.kuwo.cn/openapi/v1/recommend/music/similar';
        $params = [
            'type' => 'similar_music',
            'source' => 'kwplayer_ar_11.2.6.0_honor.apk',
            'prod' => 'kwplayer_ar_11.2.6.0',
            'platform' => 'ar',
            'corp' => 'kuwo',
            'approval' => 'false',
            'rid' => $rid,
            'num' => $num,
            'pagefrom' => 'kwplaypage',
            'from' => 'kwplayer_ar_11.2.6.0',
            'jfencv' => 'oaid'
        ];
        
        $url = $baseUrl . '?' . http_build_query($params);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('请求失败');
        }
        
        $data = json_decode($response, true);
        
        if (!$data || $data['code'] !== 200) {
            throw new Exception('API返回错误');
        }
        
        $songs = [];
        if (isset($data['data']['list'])) {
            foreach ($data['data']['list'] as $song) {
                $songs[] = [
                    'name' => $song['name'] ?? '',
                    'rid' => $song['rid'] ?? '',
                    'artist' => $song['artist'] ?? '',
                    'album' => $song['album'] ?? '',
                    'duration' => $song['duration'] ?? '',
                    'img' => $song['img'] ?? '',
                    'duration_formatted' => isset($song['duration']) ? sprintf('%d:%02d', floor($song['duration'] / 60), $song['duration'] % 60) : ''
                ];
            }
        }
        
        echo json_encode([
            'code' => 200,
            'success' => true,
            'message' => '获取相似歌曲成功',
            'data' => [
                'total' => count($songs),
                'songs' => $songs
            ],
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
        
    } else {
        throw new Exception('不支持的操作类型');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
}
?>
