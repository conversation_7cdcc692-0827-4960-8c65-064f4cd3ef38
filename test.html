<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酷我音乐API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h2 {
            color: #666;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .song-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        .song-img {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            margin-right: 15px;
            object-fit: cover;
        }
        .song-info {
            flex: 1;
        }
        .song-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .song-artist {
            color: #666;
            margin-bottom: 3px;
        }
        .song-album {
            color: #999;
            font-size: 12px;
        }
        .song-meta {
            text-align: right;
            color: #999;
            font-size: 12px;
        }
        .category-section {
            margin-bottom: 25px;
        }
        .category-title {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .tag {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 10px;
        }
        .tag.hot {
            background: #ff6b6b;
            color: white;
        }
        .tag.rank {
            background: #ffd93d;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 酷我音乐API测试工具</h1>
        
        <!-- 每日推荐测试 -->
        <div class="section">
            <h2>📅 每日推荐歌曲</h2>
            <button onclick="getDailyRecommendations()">获取每日推荐</button>
            <div id="dailyResult" class="result" style="display:none;"></div>
        </div>
        
        <!-- 相似歌曲测试 -->
        <div class="section">
            <h2>🎯 相似歌曲推荐</h2>
            <div class="form-group">
                <label for="songId">歌曲ID:</label>
                <input type="number" id="songId" placeholder="请输入歌曲ID，例如：228908" value="228908">
            </div>
            <div class="form-group">
                <label for="songNum">推荐数量:</label>
                <select id="songNum">
                    <option value="3">3首</option>
                    <option value="5">5首</option>
                    <option value="10">10首</option>
                </select>
            </div>
            <button onclick="getSimilarSongs()">获取相似歌曲</button>
            <div id="similarResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // 获取每日推荐
        async function getDailyRecommendations() {
            const resultDiv = document.getElementById('dailyResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>正在获取每日推荐...</p>';
            
            try {
                const response = await fetch('kuwo_integrated.php?action=daily');
                const data = await response.json();
                
                if (data.success) {
                    displayDailyRecommendations(data.data);
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">错误: ${data.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">请求失败: ${error.message}</p>`;
            }
        }
        
        // 获取相似歌曲
        async function getSimilarSongs() {
            const songId = document.getElementById('songId').value;
            const songNum = document.getElementById('songNum').value;
            const resultDiv = document.getElementById('similarResult');
            
            if (!songId) {
                alert('请输入歌曲ID');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>正在获取相似歌曲...</p>';
            
            try {
                const response = await fetch(`kuwo_integrated.php?action=similar&id=${songId}&num=${songNum}`);
                const data = await response.json();
                
                if (data.success) {
                    displaySimilarSongs(data.data);
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">错误: ${data.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">请求失败: ${error.message}</p>`;
            }
        }
        
        // 显示每日推荐
        function displayDailyRecommendations(data) {
            const resultDiv = document.getElementById('dailyResult');
            let html = `<h3>📊 统计信息</h3>`;
            html += `<p>总共找到 ${data.total} 首歌曲</p>`;

            // 显示统计信息
            if (data.statistics) {
                html += `<div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0;">`;
                html += `<h4>🏷️ 标签统计</h4>`;
                for (const [tag, count] of Object.entries(data.statistics.tag_stats || {})) {
                    html += `<span class="tag" style="margin: 2px;">${tag} (${count})</span>`;
                }

                html += `<h4 style="margin-top: 15px;">🏆 榜单统计</h4>`;
                for (const [bill, count] of Object.entries(data.statistics.bill_stats || {})) {
                    html += `<span class="tag rank" style="margin: 2px;">${bill} (${count})</span>`;
                }
                html += `</div>`;
            }

            // 显示section信息
            if (data.sections && data.sections.length > 0) {
                html += `<div style="background: #e3f2fd; padding: 15px; border-radius: 6px; margin: 15px 0;">`;
                html += `<h4>📂 推荐板块</h4>`;
                data.sections.forEach(section => {
                    html += `<div style="margin: 5px 0;">`;
                    html += `<strong>${section.label}</strong>`;
                    if (section.psrc) html += ` - ${section.psrc}`;
                    html += ` <small>(${section.type})</small>`;
                    html += `</div>`;
                });
                html += `</div>`;
            }

            // 按分类显示
            for (const [category, songs] of Object.entries(data.categories)) {
                if (songs.length > 0) {
                    html += `<div class="category-section">`;
                    html += `<div class="category-title">${category} (${songs.length}首)</div>`;

                    songs.forEach(song => {
                        html += createSongItem(song);
                    });

                    html += `</div>`;
                }
            }

            resultDiv.innerHTML = html;
        }
        
        // 显示相似歌曲
        function displaySimilarSongs(data) {
            const resultDiv = document.getElementById('similarResult');
            let html = `<h3>${data.rec_title}</h3>`;
            html += `<p>原歌曲ID: ${data.original_rid}，找到 ${data.total} 首相似歌曲</p>`;
            
            data.songs.forEach(song => {
                html += createSongItem(song);
            });
            
            resultDiv.innerHTML = html;
        }
        
        // 创建歌曲项目HTML
        function createSongItem(song) {
            const duration = song.duration_formatted || (song.duration ? `${Math.floor(song.duration / 60)}:${(song.duration % 60).toString().padStart(2, '0')}` : '');
            const tags = [];

            if (song.tag_name) {
                const tagClass = song.tag_name.includes('热门') ? 'hot' : '';
                tags.push(`<span class="tag ${tagClass}">${song.tag_name}</span>`);
            }
            if (song.tag_name_base && song.tag_name_rank) {
                tags.push(`<span class="tag rank">${song.tag_name_base} ${song.tag_name_rank}</span>`);
            }
            if (song.free) {
                tags.push(`<span class="tag">免费</span>`);
            }
            if (song.quality_levels && song.quality_levels.length > 0) {
                tags.push(`<span class="tag" style="background: #4caf50; color: white;">${song.quality_levels.join('/')}</span>`);
            }
            if (song.recordtime) {
                const year = new Date(song.recordtime).getFullYear();
                if (year && year > 1900) {
                    tags.push(`<span class="tag" style="background: #9e9e9e; color: white;">${year}</span>`);
                }
            }

            return `
                <div class="song-item">
                    <img src="${song.img || 'https://via.placeholder.com/60x60?text=♪'}" alt="专辑封面" class="song-img" onerror="this.src='https://via.placeholder.com/60x60?text=♪'">
                    <div class="song-info">
                        <div class="song-name">${song.name}${tags.join('')}</div>
                        <div class="song-artist">🎤 ${song.artist}</div>
                        <div class="song-album">💿 ${song.album}</div>
                        ${song.format ? `<div style="font-size: 11px; color: #999;">格式: ${song.format.toUpperCase()}</div>` : ''}
                    </div>
                    <div class="song-meta">
                        <div>ID: ${song.rid}</div>
                        <div>⏱️ ${duration}</div>
                        ${song.has_mv ? '<div>📹 有MV</div>' : ''}
                        ${song.track ? `<div>Track: ${song.track}</div>` : ''}
                        ${song.kmark && song.kmark !== '0' ? `<div>⭐ ${song.kmark}</div>` : ''}
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
