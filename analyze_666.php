<?php
/**
 * 分析666.txt文件内容并生成JSON格式的歌曲信息
 */

// 读取文件内容
$content = file_get_contents('666.txt');

// 使用正则表达式提取歌曲信息
preg_match_all('/<music[^>]*>/', $content, $matches);

$songs = [];
$categories = [];

foreach ($matches[0] as $musicTag) {
    // 提取歌曲属性
    $song = [];
    
    // 歌曲名称
    if (preg_match('/name="([^"]*)"/', $musicTag, $nameMatch)) {
        $song['name'] = html_entity_decode($nameMatch[1]);
    }
    
    // 歌曲ID
    if (preg_match('/id="([^"]*)"/', $musicTag, $idMatch)) {
        $song['rid'] = $idMatch[1];
    }
    
    // 歌手
    if (preg_match('/artist="([^"]*)"/', $musicTag, $artistMatch)) {
        $song['artist'] = html_entity_decode($artistMatch[1]);
    }
    
    // 歌手ID
    if (preg_match('/artistid="([^"]*)"/', $musicTag, $artistIdMatch)) {
        $song['artistid'] = $artistIdMatch[1];
    }
    
    // 专辑
    if (preg_match('/album="([^"]*)"/', $musicTag, $albumMatch)) {
        $song['album'] = html_entity_decode($albumMatch[1]);
    }
    
    // 专辑ID
    if (preg_match('/albumid="([^"]*)"/', $musicTag, $albumIdMatch)) {
        $song['albumid'] = $albumIdMatch[1];
    }
    
    // 时长
    if (preg_match('/duration="([^"]*)"/', $musicTag, $durationMatch)) {
        $song['duration'] = $durationMatch[1];
    }
    
    // 专辑图片
    if (preg_match('/img="([^"]*)"/', $musicTag, $imgMatch)) {
        $song['img'] = $imgMatch[1];
    }
    
    // 视频ID
    if (preg_match('/vid="([^"]*)"/', $musicTag, $vidMatch)) {
        $song['vid'] = $vidMatch[1];
    }
    
    // 视频音质
    if (preg_match('/mvquality="([^"]*)"/', $musicTag, $mvqualityMatch)) {
        $song['mvquality'] = $mvqualityMatch[1];
    }
    
    // 音质信息
    if (preg_match('/minfo="([^"]*)"/', $musicTag, $minfoMatch)) {
        $song['minfo'] = html_entity_decode($minfoMatch[1]);
    }
    
    // 标签信息
    if (preg_match('/tag_name="([^"]*)"/', $musicTag, $tagMatch)) {
        $song['tag_name'] = html_entity_decode($tagMatch[1]);
    }
    
    // 榜单信息
    if (preg_match('/tag_name_base="([^"]*)"/', $musicTag, $tagBaseMatch)) {
        $song['tag_name_base'] = html_entity_decode($tagBaseMatch[1]);
    }
    
    if (preg_match('/tag_name_rank="([^"]*)"/', $musicTag, $tagRankMatch)) {
        $song['tag_name_rank'] = html_entity_decode($tagRankMatch[1]);
    }
    
    // 是否免费
    if (preg_match('/free="([^"]*)"/', $musicTag, $freeMatch)) {
        $song['free'] = $freeMatch[1] === 'true';
    }
    
    $songs[] = $song;
}

// 提取分类信息
$categoryPatterns = [
    '每日推荐' => '/name="每日推荐"/',
    '猜你喜欢' => '/name="猜你喜欢"/',
    '超好听的单曲' => '/label="超好听的单曲"/',
    '百万收藏' => '/name="百万收藏"/',
    '新歌推荐' => '/name="新歌推荐"/',
    '歌手' => '/name="歌手"/',
    '榜单' => '/name="榜单"/',
    '歌单' => '/name="歌单"/',
    '听吧' => '/name="听吧"/',
    '抖音' => '/name="抖音"/',
    'DJ' => '/name="DJ"/',
    '儿童' => '/name="儿童"/',
    '专区' => '/name="专区"/',
    '相声' => '/name="相声"/',
    '小说' => '/name="小说"/'
];

foreach ($categoryPatterns as $categoryName => $pattern) {
    if (preg_match($pattern, $content)) {
        $categories[] = [
            'name' => $categoryName,
            'type' => 'category'
        ];
    }
}

// 构建最终结果
$result = [
    'code' => 200,
    'success' => true,
    'message' => '解析成功',
    'data' => [
        'categories' => $categories,
        'songs' => $songs,
        'total_songs' => count($songs),
        'summary' => [
            '每日推荐' => '基于用户喜好的个性化推荐',
            '超好听的单曲' => '热门单曲推荐',
            '猜你喜欢' => '智能推荐电台',
            '分类导航' => '包含歌手、榜单、歌单、听吧、抖音、DJ、儿童、专区、相声、小说等'
        ]
    ],
    'timestamp' => time()
];

// 输出JSON
header('Content-Type: application/json; charset=utf-8');
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
