<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * 解析666.txt文件获取每日推荐歌曲
 */
function getDailyRecommendations() {
    $filePath = '666.txt';
    
    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => '文件不存在'];
    }
    
    $content = file_get_contents($filePath);
    if ($content === false) {
        return ['success' => false, 'message' => '无法读取文件'];
    }
    
    // 使用正则表达式提取歌曲信息
    preg_match_all('/<music[^>]*>/', $content, $matches);
    
    $songs = [];
    foreach ($matches[0] as $musicTag) {
        $song = [];
        
        // 提取各种属性
        $attributes = [
            'name' => 'name',
            'rid' => 'id', 
            'artist' => 'artist',
            'artistid' => 'artistid',
            'album' => 'album',
            'albumid' => 'albumid',
            'duration' => 'duration',
            'img' => 'img',
            'vid' => 'vid',
            'mvquality' => 'mvquality',
            'minfo' => 'minfo'
        ];
        
        foreach ($attributes as $key => $attr) {
            if (preg_match('/' . $attr . '="([^"]*)"/', $musicTag, $match)) {
                $song[$key] = html_entity_decode($match[1]);
            }
        }
        
        // 添加标签信息
        if (preg_match('/tag_name="([^"]*)"/', $musicTag, $tagMatch)) {
            $song['tag_name'] = html_entity_decode($tagMatch[1]);
        }
        
        if (preg_match('/tag_name_base="([^"]*)"/', $musicTag, $tagBaseMatch)) {
            $song['tag_name_base'] = html_entity_decode($tagBaseMatch[1]);
            if (preg_match('/tag_name_rank="([^"]*)"/', $musicTag, $tagRankMatch)) {
                $song['tag_name_rank'] = html_entity_decode($tagRankMatch[1]);
            }
        }
        
        // 是否免费
        if (preg_match('/free="([^"]*)"/', $musicTag, $freeMatch)) {
            $song['free'] = $freeMatch[1] === 'true';
        }
        
        $songs[] = $song;
    }
    
    return ['success' => true, 'songs' => $songs];
}

/**
 * 获取相似歌曲推荐
 */
function getSimilarMusic($rid, $num) {
    $baseUrl = 'http://wapi.kuwo.cn/openapi/v1/recommend/music/similar';
    $params = [
        'type' => 'similar_music',
        'source' => 'kwplayer_ar_11.2.6.0_honor.apk',
        'prod' => 'kwplayer_ar_11.2.6.0',
        'platform' => 'ar',
        'corp' => 'kuwo',
        'approval' => 'false',
        'rid' => $rid,
        'num' => $num,
        'pagefrom' => 'kwplaypage',
        'from' => 'kwplayer_ar_11.2.6.0',
        'jfencv' => 'oaid'
    ];
    
    $url = $baseUrl . '?' . http_build_query($params);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Linux; Android 10; Honor) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
                'Accept: application/json, text/plain, */*',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Referer: http://www.kuwo.cn/',
                'Connection: keep-alive'
            ],
            'timeout' => 30
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        return ['success' => false, 'message' => '请求失败'];
    }
    
    $data = json_decode($response, true);
    
    if (!$data || $data['code'] !== 200) {
        return ['success' => false, 'message' => 'API返回错误'];
    }
    
    return ['success' => true, 'data' => $data];
}

/**
 * 格式化歌曲数据
 */
function formatSongData($songList) {
    $formattedList = [];
    
    foreach ($songList as $song) {
        $formattedSong = [
            'name' => $song['name'] ?? '',
            'rid' => $song['rid'] ?? '',
            'artist' => $song['artist'] ?? '',
            'artistid' => $song['artistid'] ?? '',
            'album' => $song['album'] ?? '',
            'albumid' => $song['albumid'] ?? '',
            'duration' => $song['duration'] ?? '',
            'img' => $song['img'] ?? '',
            'vid' => $song['vid'] ?? '',
            'mvquality' => $song['mvquality'] ?? '',
            'minfo' => $song['minfo'] ?? ''
        ];
        
        // 添加标签信息（如果有）
        if (isset($song['tag_name'])) {
            $formattedSong['tag_name'] = $song['tag_name'];
        }
        if (isset($song['tag_name_base'])) {
            $formattedSong['tag_name_base'] = $song['tag_name_base'];
            $formattedSong['tag_name_rank'] = $song['tag_name_rank'] ?? '';
        }
        if (isset($song['free'])) {
            $formattedSong['free'] = $song['free'];
        }
        
        $formattedList[] = $formattedSong;
    }
    
    return $formattedList;
}

// 主程序逻辑
try {
    $action = $_GET['action'] ?? 'daily';
    
    switch ($action) {
        case 'daily':
            // 获取每日推荐
            $result = getDailyRecommendations();
            
            if (!$result['success']) {
                throw new Exception($result['message']);
            }
            
            $formattedSongs = formatSongData($result['songs']);
            
            // 按分类整理歌曲
            $categorizedSongs = [
                '超好听的单曲' => [],
                '热门搜索' => [],
                '热播歌曲' => [],
                '榜单歌曲' => [],
                '其他' => []
            ];
            
            foreach ($formattedSongs as $song) {
                if (isset($song['tag_name'])) {
                    if ($song['tag_name'] === '热门搜索') {
                        $categorizedSongs['热门搜索'][] = $song;
                    } elseif ($song['tag_name'] === '热播歌曲') {
                        $categorizedSongs['热播歌曲'][] = $song;
                    } else {
                        $categorizedSongs['其他'][] = $song;
                    }
                } elseif (isset($song['tag_name_base'])) {
                    $categorizedSongs['榜单歌曲'][] = $song;
                } else {
                    $categorizedSongs['超好听的单曲'][] = $song;
                }
            }
            
            $response = [
                'code' => 200,
                'success' => true,
                'message' => '获取每日推荐成功',
                'data' => [
                    'total' => count($formattedSongs),
                    'categories' => $categorizedSongs,
                    'all_songs' => $formattedSongs
                ],
                'timestamp' => time()
            ];
            break;
            
        case 'similar':
            // 获取相似歌曲
            $rid = isset($_GET['id']) ? intval($_GET['id']) : 0;
            $num = isset($_GET['num']) ? intval($_GET['num']) : 3;
            
            if ($rid <= 0) {
                throw new Exception('歌曲ID参数无效');
            }
            
            if ($num <= 0 || $num > 50) {
                $num = 3;
            }
            
            $result = getSimilarMusic($rid, $num);
            
            if (!$result['success']) {
                throw new Exception($result['message']);
            }
            
            $apiData = $result['data'];
            
            if (!isset($apiData['data']['list']) || empty($apiData['data']['list'])) {
                throw new Exception('未找到相似歌曲');
            }
            
            $formattedSongs = formatSongData($apiData['data']['list']);
            
            $response = [
                'code' => 200,
                'success' => true,
                'message' => '获取相似歌曲成功',
                'data' => [
                    'original_rid' => $apiData['data']['rid'],
                    'rec_title' => $apiData['data']['recTitle'] ?? '猜你也会喜欢',
                    'total' => count($formattedSongs),
                    'songs' => $formattedSongs
                ],
                'timestamp' => time()
            ];
            break;
            
        default:
            throw new Exception('不支持的操作类型');
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(400);
    $errorResponse = [
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null,
        'timestamp' => time()
    ];
    
    echo json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
