<?php
// 简单测试脚本
echo "测试酷我音乐API...\n";

// 测试每日推荐API
$url = 'http://localhost/kuwo_integrated.php?action=daily';

// 使用curl进行测试
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";

if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "✅ API测试成功!\n";
        echo "总歌曲数: " . $data['data']['total'] . "\n";
        echo "分类数: " . count($data['data']['categories']) . "\n";
        
        foreach ($data['data']['categories'] as $category => $songs) {
            echo "- $category: " . count($songs) . "首\n";
        }
        
        if (isset($data['data']['statistics'])) {
            echo "\n标签统计:\n";
            foreach ($data['data']['statistics']['tag_stats'] as $tag => $count) {
                echo "- $tag: $count\n";
            }
        }
    } else {
        echo "❌ API返回错误: " . ($data['message'] ?? '未知错误') . "\n";
    }
} else {
    echo "❌ 请求失败\n";
}
?>
