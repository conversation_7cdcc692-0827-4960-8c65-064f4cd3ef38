<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * 从酷我音乐API获取每日推荐歌曲
 */
function getDailyRecommendations() {
    $apiUrl = 'http://homepages.kuwo.cn/mgxh.s?type=tuijian11&apiv=17&dailyrec=1&notrace=0';

    // 设置请求头，模拟移动端请求
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Linux; Android 10; Honor) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
                'Accept: application/xml, text/xml, */*',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Referer: http://www.kuwo.cn/',
                'Connection: keep-alive'
            ],
            'timeout' => 30
        ]
    ]);

    // 发送请求
    $content = @file_get_contents($apiUrl, false, $context);

    if ($content === false) {
        return ['success' => false, 'message' => '无法获取API数据'];
    }

    // 解析XML内容
    return parseKuwoXmlContent($content);
}

/**
 * 解析酷我音乐XML内容
 */
function parseKuwoXmlContent($content) {
    // 使用正则表达式提取歌曲信息
    preg_match_all('/<music[^>]*>/', $content, $matches);

    $songs = [];
    $sections = [];

    // 提取section信息
    preg_match_all('/<section[^>]*>/', $content, $sectionMatches);
    foreach ($sectionMatches[0] as $sectionTag) {
        $section = [];

        if (preg_match('/type="([^"]*)"/', $sectionTag, $typeMatch)) {
            $section['type'] = $typeMatch[1];
        }
        if (preg_match('/label="([^"]*)"/', $sectionTag, $labelMatch)) {
            $section['label'] = html_entity_decode($labelMatch[1]);
        }
        if (preg_match('/psrc="([^"]*)"/', $sectionTag, $psrcMatch)) {
            $section['psrc'] = html_entity_decode($psrcMatch[1]);
        }
        if (preg_match('/section_id="([^"]*)"/', $sectionTag, $idMatch)) {
            $section['section_id'] = $idMatch[1];
        }

        $sections[] = $section;
    }

    foreach ($matches[0] as $musicTag) {
        $song = [];

        // 提取各种属性
        $attributes = [
            'name' => 'name',
            'rid' => 'id',
            'artist' => 'artist',
            'artistid' => 'artistid',
            'album' => 'album',
            'albumid' => 'albumid',
            'duration' => 'duration',
            'img' => 'img',
            'vid' => 'vid',
            'mvquality' => 'mvquality',
            'minfo' => 'minfo',
            'format' => 'format',
            'pay_flag' => 'pay_flag',
            'content_type' => 'content_type',
            'mvflag' => 'mvflag',
            'kmark' => 'kmark',
            'track' => 'track'
        ];

        foreach ($attributes as $key => $attr) {
            if (preg_match('/' . $attr . '="([^"]*)"/', $musicTag, $match)) {
                $song[$key] = html_entity_decode($match[1]);
            }
        }

        // 添加标签信息
        if (preg_match('/tag_name="([^"]*)"/', $musicTag, $tagMatch)) {
            $song['tag_name'] = html_entity_decode($tagMatch[1]);
        }

        if (preg_match('/tag_type="([^"]*)"/', $musicTag, $tagTypeMatch)) {
            $song['tag_type'] = html_entity_decode($tagTypeMatch[1]);
        }

        if (preg_match('/tag_name_base="([^"]*)"/', $musicTag, $tagBaseMatch)) {
            $song['tag_name_base'] = html_entity_decode($tagBaseMatch[1]);
            if (preg_match('/tag_name_rank="([^"]*)"/', $musicTag, $tagRankMatch)) {
                $song['tag_name_rank'] = html_entity_decode($tagRankMatch[1]);
            }
        }

        if (preg_match('/tag_id="([^"]*)"/', $musicTag, $tagIdMatch)) {
            $song['tag_id'] = $tagIdMatch[1];
        }

        // 是否免费
        if (preg_match('/free="([^"]*)"/', $musicTag, $freeMatch)) {
            $song['free'] = $freeMatch[1] === 'true';
        }

        // 发行时间
        if (preg_match('/recordtime="([^"]*)"/', $musicTag, $recordMatch)) {
            $song['recordtime'] = $recordMatch[1];
        }

        $songs[] = $song;
    }

    return ['success' => true, 'songs' => $songs, 'sections' => $sections];
}

/**
 * 获取相似歌曲推荐
 */
function getSimilarMusic($rid, $num) {
    $baseUrl = 'http://wapi.kuwo.cn/openapi/v1/recommend/music/similar';
    $params = [
        'type' => 'similar_music',
        'source' => 'kwplayer_ar_11.2.6.0_honor.apk',
        'prod' => 'kwplayer_ar_11.2.6.0',
        'platform' => 'ar',
        'corp' => 'kuwo',
        'approval' => 'false',
        'rid' => $rid,
        'num' => $num,
        'pagefrom' => 'kwplaypage',
        'from' => 'kwplayer_ar_11.2.6.0',
        'jfencv' => 'oaid'
    ];
    
    $url = $baseUrl . '?' . http_build_query($params);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Linux; Android 10; Honor) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
                'Accept: application/json, text/plain, */*',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Referer: http://www.kuwo.cn/',
                'Connection: keep-alive'
            ],
            'timeout' => 30
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        return ['success' => false, 'message' => '请求失败'];
    }
    
    $data = json_decode($response, true);
    
    if (!$data || $data['code'] !== 200) {
        return ['success' => false, 'message' => 'API返回错误'];
    }
    
    return ['success' => true, 'data' => $data];
}

/**
 * 格式化歌曲数据
 */
function formatSongData($songList) {
    $formattedList = [];

    foreach ($songList as $song) {
        $formattedSong = [
            'name' => $song['name'] ?? '',
            'rid' => $song['rid'] ?? '',
            'artist' => $song['artist'] ?? '',
            'artistid' => $song['artistid'] ?? '',
            'album' => $song['album'] ?? '',
            'albumid' => $song['albumid'] ?? '',
            'duration' => $song['duration'] ?? '',
            'img' => $song['img'] ?? '',
            'vid' => $song['vid'] ?? '',
            'mvquality' => $song['mvquality'] ?? '',
            'minfo' => $song['minfo'] ?? '',
            'format' => $song['format'] ?? '',
            'pay_flag' => $song['pay_flag'] ?? '',
            'content_type' => $song['content_type'] ?? '',
            'mvflag' => $song['mvflag'] ?? '',
            'kmark' => $song['kmark'] ?? '',
            'track' => $song['track'] ?? ''
        ];

        // 添加标签信息（如果有）
        if (isset($song['tag_name'])) {
            $formattedSong['tag_name'] = $song['tag_name'];
        }
        if (isset($song['tag_type'])) {
            $formattedSong['tag_type'] = $song['tag_type'];
        }
        if (isset($song['tag_name_base'])) {
            $formattedSong['tag_name_base'] = $song['tag_name_base'];
            $formattedSong['tag_name_rank'] = $song['tag_name_rank'] ?? '';
        }
        if (isset($song['tag_id'])) {
            $formattedSong['tag_id'] = $song['tag_id'];
        }
        if (isset($song['free'])) {
            $formattedSong['free'] = $song['free'];
        }
        if (isset($song['recordtime'])) {
            $formattedSong['recordtime'] = $song['recordtime'];
        }

        // 格式化时长为分:秒格式
        if (!empty($formattedSong['duration']) && is_numeric($formattedSong['duration'])) {
            $duration = intval($formattedSong['duration']);
            $formattedSong['duration_formatted'] = sprintf('%d:%02d',
                floor($duration / 60),
                $duration % 60
            );
        }

        // 判断是否有MV
        $formattedSong['has_mv'] = !empty($formattedSong['vid']) && $formattedSong['vid'] !== '0';

        // 判断音质等级
        if (!empty($formattedSong['minfo'])) {
            $formattedSong['quality_levels'] = [];
            if (strpos($formattedSong['minfo'], 'level:ff') !== false) {
                $formattedSong['quality_levels'][] = '无损';
            }
            if (strpos($formattedSong['minfo'], 'level:p') !== false) {
                $formattedSong['quality_levels'][] = '高品质';
            }
            if (strpos($formattedSong['minfo'], 'level:h') !== false) {
                $formattedSong['quality_levels'][] = '标准';
            }
        }

        $formattedList[] = $formattedSong;
    }

    return $formattedList;
}

// 主程序逻辑
try {
    $action = $_GET['action'] ?? 'daily';
    
    switch ($action) {
        case 'daily':
            // 获取每日推荐
            $result = getDailyRecommendations();

            if (!$result['success']) {
                throw new Exception($result['message']);
            }

            $formattedSongs = formatSongData($result['songs']);

            // 按分类整理歌曲 - 更详细的分类
            $categorizedSongs = [
                '你的私荐歌曲' => [],
                '热门搜索' => [],
                '热播歌曲' => [],
                '热门分享' => [],
                '本周热播' => [],
                '昨日热播' => [],
                '榜单歌曲' => [],
                '经典华语' => [],
                'K歌热门' => [],
                '感恩父母' => [],
                '歌手热门' => [],
                '其他推荐' => []
            ];

            // 统计各种标签
            $tagStats = [];
            $billStats = [];

            foreach ($formattedSongs as $song) {
                // 统计标签
                if (isset($song['tag_name'])) {
                    $tagStats[$song['tag_name']] = ($tagStats[$song['tag_name']] ?? 0) + 1;
                }

                // 统计榜单
                if (isset($song['tag_name_base'])) {
                    $billStats[$song['tag_name_base']] = ($billStats[$song['tag_name_base']] ?? 0) + 1;
                }

                // 分类歌曲
                if (isset($song['tag_name'])) {
                    switch ($song['tag_name']) {
                        case '热门搜索':
                            $categorizedSongs['热门搜索'][] = $song;
                            break;
                        case '热播歌曲':
                            $categorizedSongs['热播歌曲'][] = $song;
                            break;
                        case '热门分享':
                            $categorizedSongs['热门分享'][] = $song;
                            break;
                        case strpos($song['tag_name'], '本周') !== false:
                            $categorizedSongs['本周热播'][] = $song;
                            break;
                        case strpos($song['tag_name'], '昨日') !== false:
                            $categorizedSongs['昨日热播'][] = $song;
                            break;
                        default:
                            $categorizedSongs['其他推荐'][] = $song;
                    }
                } elseif (isset($song['tag_name_base'])) {
                    // 根据榜单名称分类
                    $billName = $song['tag_name_base'];
                    if (strpos($billName, 'K歌') !== false) {
                        $categorizedSongs['K歌热门'][] = $song;
                    } elseif (strpos($billName, '感恩父母') !== false) {
                        $categorizedSongs['感恩父母'][] = $song;
                    } elseif (strpos($billName, '经典华语') !== false || strpos($billName, '华语') !== false) {
                        $categorizedSongs['经典华语'][] = $song;
                    } elseif (strpos($billName, '最热') !== false) {
                        $categorizedSongs['歌手热门'][] = $song;
                    } else {
                        $categorizedSongs['榜单歌曲'][] = $song;
                    }
                } else {
                    $categorizedSongs['你的私荐歌曲'][] = $song;
                }
            }

            // 移除空分类
            $categorizedSongs = array_filter($categorizedSongs, function($songs) {
                return !empty($songs);
            });

            // 获取section信息
            $sectionInfo = [];
            if (isset($result['sections'])) {
                foreach ($result['sections'] as $section) {
                    if (isset($section['label'])) {
                        $sectionInfo[] = [
                            'type' => $section['type'] ?? '',
                            'label' => $section['label'],
                            'psrc' => $section['psrc'] ?? '',
                            'section_id' => $section['section_id'] ?? ''
                        ];
                    }
                }
            }

            $response = [
                'code' => 200,
                'success' => true,
                'message' => '获取每日推荐成功',
                'data' => [
                    'total' => count($formattedSongs),
                    'categories' => $categorizedSongs,
                    'all_songs' => $formattedSongs,
                    'sections' => $sectionInfo,
                    'statistics' => [
                        'tag_stats' => $tagStats,
                        'bill_stats' => $billStats,
                        'category_counts' => array_map('count', $categorizedSongs)
                    ]
                ],
                'timestamp' => time()
            ];
            break;
            
        case 'similar':
            // 获取相似歌曲
            $rid = isset($_GET['id']) ? intval($_GET['id']) : 0;
            $num = isset($_GET['num']) ? intval($_GET['num']) : 3;
            
            if ($rid <= 0) {
                throw new Exception('歌曲ID参数无效');
            }
            
            if ($num <= 0 || $num > 50) {
                $num = 3;
            }
            
            $result = getSimilarMusic($rid, $num);
            
            if (!$result['success']) {
                throw new Exception($result['message']);
            }
            
            $apiData = $result['data'];
            
            if (!isset($apiData['data']['list']) || empty($apiData['data']['list'])) {
                throw new Exception('未找到相似歌曲');
            }
            
            $formattedSongs = formatSongData($apiData['data']['list']);
            
            $response = [
                'code' => 200,
                'success' => true,
                'message' => '获取相似歌曲成功',
                'data' => [
                    'original_rid' => $apiData['data']['rid'],
                    'rec_title' => $apiData['data']['recTitle'] ?? '猜你也会喜欢',
                    'total' => count($formattedSongs),
                    'songs' => $formattedSongs
                ],
                'timestamp' => time()
            ];
            break;
            
        default:
            throw new Exception('不支持的操作类型');
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(400);
    $errorResponse = [
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null,
        'timestamp' => time()
    ];
    
    echo json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
