<?php
// 测试解析777.txt文件
$content = file_get_contents('777.txt');

echo "文件大小: " . strlen($content) . " 字节\n";

// 测试提取music标签
preg_match_all('/<music[^>]*>/s', $content, $musicTags);

echo "找到 " . count($musicTags[0]) . " 个music标签\n";

if (count($musicTags[0]) > 0) {
    echo "\n前3个music标签的解析结果:\n";
    
    for ($i = 0; $i < min(3, count($musicTags[0])); $i++) {
        $tag = $musicTags[0][$i];
        echo "\n=== 歌曲 " . ($i + 1) . " ===\n";
        
        // 提取歌曲名
        if (preg_match('/name="([^"]*)"/', $tag, $match)) {
            echo "歌曲名: " . html_entity_decode($match[1]) . "\n";
        }
        
        // 提取歌手
        if (preg_match('/artist="([^"]*)"/', $tag, $match)) {
            echo "歌手: " . html_entity_decode($match[1]) . "\n";
        }
        
        // 提取ID
        if (preg_match('/\bid="([^"]*)"/', $tag, $match)) {
            echo "ID: " . $match[1] . "\n";
        }
        
        // 提取专辑
        if (preg_match('/album="([^"]*)"/', $tag, $match)) {
            echo "专辑: " . html_entity_decode($match[1]) . "\n";
        }
        
        // 提取时长
        if (preg_match('/duration="([^"]*)"/', $tag, $match)) {
            echo "时长: " . $match[1] . "秒\n";
        }
        
        // 提取标签
        if (preg_match('/tag_name="([^"]*)"/', $tag, $match)) {
            echo "标签: " . html_entity_decode($match[1]) . "\n";
        }
        
        // 提取榜单
        if (preg_match('/tag_name_base="([^"]*)"/', $tag, $match)) {
            echo "榜单: " . html_entity_decode($match[1]);
            if (preg_match('/tag_name_rank="([^"]*)"/', $tag, $rankMatch)) {
                echo " " . html_entity_decode($rankMatch[1]);
            }
            echo "\n";
        }
    }
} else {
    echo "没有找到music标签，检查文件内容...\n";
    echo "文件开头100字符: " . substr($content, 0, 100) . "\n";
}
?>
