<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * 从酷我音乐API获取每日推荐歌曲
 */
function getDailyRecommendations() {
    $apiUrl = 'http://homepages.kuwo.cn/mgxh.s?type=tuijian11&apiv=17&dailyrec=1&notrace=0';

    // 设置请求头，模拟移动端请求
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Linux; Android 10; Honor) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
                'Accept: application/xml, text/xml, */*',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Referer: http://www.kuwo.cn/',
                'Connection: keep-alive'
            ],
            'timeout' => 30
        ]
    ]);

    // 发送请求
    $content = @file_get_contents($apiUrl, false, $context);

    if ($content === false) {
        return ['success' => false, 'message' => '无法获取API数据'];
    }

    // 解析XML内容
    return parseKuwoXmlContent($content);
}

/**
 * 解析酷我音乐XML内容 - 优化版本
 */
function parseKuwoXmlContent($content) {
    $songs = [];

    // 一次性提取所有music标签
    preg_match_all('/<music[^>]*>/', $content, $matches);

    foreach ($matches[0] as $musicTag) {
        // 一次性提取所有属性
        preg_match_all('/(\w+)="([^"]*)"/', $musicTag, $attrMatches, PREG_SET_ORDER);

        $song = [];
        foreach ($attrMatches as $attr) {
            $key = $attr[1];
            $value = html_entity_decode($attr[2]);

            // 只保留需要的字段
            switch ($key) {
                case 'name':
                case 'artist':
                case 'album':
                case 'duration':
                case 'img':
                case 'vid':
                case 'mvquality':
                case 'tag_name':
                case 'tag_name_base':
                case 'tag_name_rank':
                case 'free':
                    $song[$key] = $value;
                    break;
                case 'id':
                    $song['rid'] = $value;
                    break;
                case 'artistid':
                case 'albumid':
                    $song[$key] = $value;
                    break;
            }
        }

        // 简单处理免费标识
        if (isset($song['free'])) {
            $song['free'] = $song['free'] === 'true';
        }

        $songs[] = $song;
    }

    return ['success' => true, 'songs' => $songs];
}

/**
 * 获取相似歌曲推荐
 */
function getSimilarMusic($rid, $num) {
    $baseUrl = 'http://wapi.kuwo.cn/openapi/v1/recommend/music/similar';
    $params = [
        'type' => 'similar_music',
        'source' => 'kwplayer_ar_11.2.6.0_honor.apk',
        'prod' => 'kwplayer_ar_11.2.6.0',
        'platform' => 'ar',
        'corp' => 'kuwo',
        'approval' => 'false',
        'rid' => $rid,
        'num' => $num,
        'pagefrom' => 'kwplaypage',
        'from' => 'kwplayer_ar_11.2.6.0',
        'jfencv' => 'oaid'
    ];
    
    $url = $baseUrl . '?' . http_build_query($params);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Linux; Android 10; Honor) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
                'Accept: application/json, text/plain, */*',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Referer: http://www.kuwo.cn/',
                'Connection: keep-alive'
            ],
            'timeout' => 30
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        return ['success' => false, 'message' => '请求失败'];
    }
    
    $data = json_decode($response, true);
    
    if (!$data || $data['code'] !== 200) {
        return ['success' => false, 'message' => 'API返回错误'];
    }
    
    return ['success' => true, 'data' => $data];
}

/**
 * 格式化歌曲数据 - 简化版本
 */
function formatSongData($songList) {
    $formattedList = [];

    foreach ($songList as $song) {
        // 直接使用原始数据，只做必要的格式化
        $formattedSong = $song;

        // 简单格式化时长
        if (!empty($song['duration']) && is_numeric($song['duration'])) {
            $duration = intval($song['duration']);
            $formattedSong['duration_formatted'] = sprintf('%d:%02d',
                floor($duration / 60),
                $duration % 60
            );
        }

        // 判断是否有MV
        $formattedSong['has_mv'] = !empty($song['vid']) && $song['vid'] !== '0';

        $formattedList[] = $formattedSong;
    }

    return $formattedList;
}

// 主程序逻辑
try {
    $action = $_GET['action'] ?? 'daily';
    
    switch ($action) {
        case 'daily':
            // 获取每日推荐
            $result = getDailyRecommendations();

            if (!$result['success']) {
                throw new Exception($result['message']);
            }

            // 简单分类，不做统计
            $categories = [
                '热门搜索' => [],
                '热播歌曲' => [],
                '热门分享' => [],
                '榜单歌曲' => [],
                '推荐歌曲' => []
            ];

            foreach ($result['songs'] as $song) {
                if (isset($song['tag_name'])) {
                    switch ($song['tag_name']) {
                        case '热门搜索':
                            $categories['热门搜索'][] = $song;
                            break;
                        case '热播歌曲':
                            $categories['热播歌曲'][] = $song;
                            break;
                        case '热门分享':
                            $categories['热门分享'][] = $song;
                            break;
                        default:
                            $categories['推荐歌曲'][] = $song;
                    }
                } elseif (isset($song['tag_name_base'])) {
                    $categories['榜单歌曲'][] = $song;
                } else {
                    $categories['推荐歌曲'][] = $song;
                }
            }

            // 移除空分类
            $categories = array_filter($categories, function($songs) {
                return !empty($songs);
            });

            $response = [
                'code' => 200,
                'success' => true,
                'message' => '获取每日推荐成功',
                'data' => [
                    'total' => count($result['songs']),
                    'categories' => $categories,
                    'all_songs' => $result['songs']
                ],
                'timestamp' => time()
            ];
            break;
            
        case 'similar':
            // 获取相似歌曲
            $rid = isset($_GET['id']) ? intval($_GET['id']) : 0;
            $num = isset($_GET['num']) ? intval($_GET['num']) : 3;
            
            if ($rid <= 0) {
                throw new Exception('歌曲ID参数无效');
            }
            
            if ($num <= 0 || $num > 50) {
                $num = 3;
            }
            
            $result = getSimilarMusic($rid, $num);
            
            if (!$result['success']) {
                throw new Exception($result['message']);
            }
            
            $apiData = $result['data'];
            
            if (!isset($apiData['data']['list']) || empty($apiData['data']['list'])) {
                throw new Exception('未找到相似歌曲');
            }
            
            $formattedSongs = formatSongData($apiData['data']['list']);
            
            $response = [
                'code' => 200,
                'success' => true,
                'message' => '获取相似歌曲成功',
                'data' => [
                    'original_rid' => $apiData['data']['rid'],
                    'rec_title' => $apiData['data']['recTitle'] ?? '猜你也会喜欢',
                    'total' => count($formattedSongs),
                    'songs' => $formattedSongs
                ],
                'timestamp' => time()
            ];
            break;
            
        default:
            throw new Exception('不支持的操作类型');
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(400);
    $errorResponse = [
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null,
        'timestamp' => time()
    ];
    
    echo json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
