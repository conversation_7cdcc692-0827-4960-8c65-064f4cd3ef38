<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * 获取相似歌曲推荐
 * @param int $rid 歌曲ID
 * @param int $num 返回数量
 * @return array
 */
function getSimilarMusic($rid, $num) {
    // 构建API URL
    $baseUrl = 'http://wapi.kuwo.cn/openapi/v1/recommend/music/similar';
    $params = [
        'type' => 'similar_music',
        'source' => 'kwplayer_ar_11.2.6.0_honor.apk',
        'prod' => 'kwplayer_ar_11.2.6.0',
        'platform' => 'ar',
        'corp' => 'kuwo',
        'approval' => 'false',
        'rid' => $rid,
        'num' => $num,
        'pagefrom' => 'kwplaypage',
        'from' => 'kwplayer_ar_11.2.6.0',
        'jfencv' => 'oaid'
    ];

    $url = $baseUrl . '?' . http_build_query($params);

    // 设置请求头，模拟移动端请求
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Linux; Android 10; Honor) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
                'Accept: application/json, text/plain, */*',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Referer: http://www.kuwo.cn/',
                'Connection: keep-alive'
            ],
            'timeout' => 30
        ]
    ]);

    // 发送请求
    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        return [
            'success' => false,
            'message' => '请求失败，无法获取数据'
        ];
    }

    $data = json_decode($response, true);

    if (!$data || $data['code'] !== 200) {
        return [
            'success' => false,
            'message' => 'API返回错误：' . ($data['msg'] ?? '未知错误')
        ];
    }

    return [
        'success' => true,
        'data' => $data
    ];
}

/**
 * 格式化歌曲数据
 * @param array $songList 原始歌曲列表
 * @return array
 */
function formatSongData($songList) {
    $formattedList = [];

    foreach ($songList as $song) {
        $formattedSong = [
            'name' => $song['name'] ?? '',
            'rid' => $song['rid'] ?? '',
            'artist' => $song['artist'] ?? '',
            'artistid' => $song['artistid'] ?? '',
            'album' => $song['album'] ?? '',
            'albumid' => $song['albumid'] ?? '',
            'duration' => $song['duration'] ?? '',
            'img' => $song['img'] ?? '',
            'vid' => $song['vid'] ?? '',
            'mvquality' => $song['mvquality'] ?? '',
            'minfo' => $song['minfo'] ?? ''
        ];

        $formattedList[] = $formattedSong;
    }

    return $formattedList;
}

// 主程序逻辑
try {
    // 获取GET参数
    $rid = isset($_GET['id']) ? intval($_GET['id']) : 0;
    $num = isset($_GET['num']) ? intval($_GET['num']) : 3;

    // 参数验证
    if ($rid <= 0) {
        throw new Exception('歌曲ID参数无效，请提供有效的id参数');
    }

    if ($num <= 0 || $num > 50) {
        $num = 3; // 默认返回3首
    }

    // 获取相似歌曲数据
    $result = getSimilarMusic($rid, $num);

    if (!$result['success']) {
        throw new Exception($result['message']);
    }

    $apiData = $result['data'];

    // 检查是否有歌曲列表
    if (!isset($apiData['data']['list']) || empty($apiData['data']['list'])) {
        throw new Exception('未找到相似歌曲');
    }

    // 格式化返回数据
    $formattedSongs = formatSongData($apiData['data']['list']);

    // 构建响应数据
    $response = [
        'code' => 200,
        'success' => true,
        'message' => '获取成功',
        'data' => [
            'original_rid' => $apiData['data']['rid'],
            'rec_title' => $apiData['data']['recTitle'] ?? '猜你也会喜欢',
            'total' => count($formattedSongs),
            'songs' => $formattedSongs
        ],
        'timestamp' => time()
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

} catch (Exception $e) {
    // 错误处理
    http_response_code(400);
    $errorResponse = [
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null,
        'timestamp' => time()
    ];

    echo json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>