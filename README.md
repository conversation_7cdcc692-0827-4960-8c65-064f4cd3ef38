# 🎵 酷我音乐API集成系统

一个完整的酷我音乐API集成解决方案，支持实时获取每日推荐和相似歌曲推荐。

## ✨ 功能特点

### 🎯 核心功能
- **实时每日推荐** - 从酷我音乐官方API获取最新推荐
- **相似歌曲推荐** - 根据歌曲ID获取相似歌曲
- **智能分类** - 自动按标签和榜单分类整理
- **详细统计** - 提供标签、榜单等统计信息

### 📊 支持的分类
- 你的私荐歌曲
- 热门搜索
- 热播歌曲  
- 热门分享
- 本周热播
- 昨日热播
- 榜单歌曲
- 经典华语
- K歌热门
- 感恩父母
- 歌手热门
- 其他推荐

### 🎼 歌曲信息字段
每首歌曲包含以下详细信息：
- `name` - 歌曲名字
- `rid` - 歌曲ID
- `artist` - 歌手
- `artistid` - 歌手ID
- `album` - 专辑
- `albumid` - 专辑ID
- `duration` - 时长（秒）
- `duration_formatted` - 格式化时长（分:秒）
- `img` - 专辑图片
- `vid` - 视频ID
- `mvquality` - 视频音质
- `minfo` - 音质详情
- `has_mv` - 是否有MV
- `quality_levels` - 音质等级数组
- `tag_name` - 标签名称
- `tag_name_base` - 榜单名称
- `tag_name_rank` - 榜单排名
- `free` - 是否免费
- `recordtime` - 发行时间
- `format` - 音频格式
- `pay_flag` - 付费标识
- `mvflag` - MV标识
- `kmark` - 评分标识
- `track` - 曲目编号

## 🚀 API接口

### 1. 获取每日推荐
```
GET kuwo_integrated.php?action=daily
```

**响应示例:**
```json
{
    "code": 200,
    "success": true,
    "message": "获取每日推荐成功",
    "data": {
        "total": 18,
        "categories": {
            "你的私荐歌曲": [...],
            "热门搜索": [...],
            "热播歌曲": [...],
            "榜单歌曲": [...]
        },
        "all_songs": [...],
        "sections": [...],
        "statistics": {
            "tag_stats": {...},
            "bill_stats": {...},
            "category_counts": {...}
        }
    },
    "timestamp": 1754207377
}
```

### 2. 获取相似歌曲
```
GET kuwo_integrated.php?action=similar&id=79479&num=5
```

**参数说明:**
- `id` - 歌曲ID（必需）
- `num` - 返回数量（可选，默认3首，最大50首）

### 3. 原始相似歌曲API
```
GET 1.php?id=79479&num=3
```

## 📱 使用方法

### 基础调用
```javascript
// 获取每日推荐
fetch('kuwo_integrated.php?action=daily')
    .then(response => response.json())
    .then(data => {
        console.log('总歌曲数:', data.data.total);
        console.log('分类:', Object.keys(data.data.categories));
    });

// 获取相似歌曲
fetch('kuwo_integrated.php?action=similar&id=79479&num=5')
    .then(response => response.json())
    .then(data => {
        console.log('相似歌曲:', data.data.songs);
    });
```

### PHP调用示例
```php
// 获取每日推荐
$response = file_get_contents('http://localhost/kuwo_integrated.php?action=daily');
$data = json_decode($response, true);

if ($data['success']) {
    foreach ($data['data']['categories'] as $category => $songs) {
        echo "$category: " . count($songs) . "首歌曲\n";
    }
}
```

## 🎨 测试界面

项目包含一个可视化测试界面 `test.html`，提供：
- 每日推荐测试
- 相似歌曲测试
- 美观的歌曲展示
- 统计信息显示
- 分类浏览

## 📁 文件结构

```
kuwo/
├── 1.php                    # 原始相似歌曲API
├── kuwo_integrated.php      # 集成API（主要接口）
├── test.html               # 可视化测试界面
├── test_api.php            # API测试脚本
└── README.md               # 说明文档
```

## 🔧 技术特点

1. **实时数据** - 直接从酷我音乐官方API获取最新数据
2. **智能解析** - 自动解析XML格式并转换为JSON
3. **详细分类** - 根据标签和榜单智能分类
4. **错误处理** - 完善的错误处理和提示
5. **跨域支持** - 支持CORS跨域请求
6. **统计分析** - 提供详细的数据统计

## 🌟 更新内容

相比之前的版本，新版本具有以下改进：

1. **实时数据源** - 不再依赖本地文件，直接从API获取
2. **更丰富的分类** - 支持12种不同的歌曲分类
3. **详细统计** - 提供标签统计、榜单统计等
4. **更多字段** - 支持更多歌曲属性字段
5. **智能格式化** - 自动格式化时长、判断MV等
6. **可视化增强** - 更美观的测试界面

## 🎯 使用建议

1. **缓存策略** - 建议对API响应进行适当缓存以提高性能
2. **错误处理** - 在生产环境中添加重试机制
3. **数据验证** - 对返回的数据进行验证和清洗
4. **监控告警** - 监控API可用性和响应时间

现在您可以享受更强大、更灵活的酷我音乐API集成体验！🎉
