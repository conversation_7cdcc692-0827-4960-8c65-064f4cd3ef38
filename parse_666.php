<?php
header('Content-Type: application/json; charset=utf-8');

/**
 * 解析666.txt文件并提取歌曲信息
 */
function parse666File() {
    $filePath = '666.txt';
    
    if (!file_exists($filePath)) {
        return [
            'success' => false,
            'message' => '文件不存在'
        ];
    }
    
    $content = file_get_contents($filePath);
    if ($content === false) {
        return [
            'success' => false,
            'message' => '无法读取文件'
        ];
    }
    
    // 解析XML
    $xml = simplexml_load_string($content);
    if ($xml === false) {
        return [
            'success' => false,
            'message' => 'XML解析失败'
        ];
    }
    
    $result = [
        'success' => true,
        'data' => [
            'sections' => [],
            'songs' => [],
            'categories' => []
        ]
    ];
    
    // 解析各个section
    foreach ($xml->section as $section) {
        $sectionData = [
            'type' => (string)$section['type'],
            'section_id' => (string)$section['section_id'],
            'weight' => (string)$section['weight']
        ];
        
        // 处理不同类型的section
        switch ($sectionData['type']) {
            case 'big_business':
                $sectionData['name'] = '主要功能区';
                $sectionData['items'] = [];
                
                // 解析每日推荐
                foreach ($section->children() as $child) {
                    $childName = $child->getName();
                    $attributes = $child->attributes();
                    
                    if ($childName === 'r10_tuijian3_songlist_weather') {
                        $sectionData['items'][] = [
                            'type' => '每日推荐',
                            'name' => (string)$attributes['name'],
                            'id' => (string)$attributes['id'],
                            'img' => (string)$attributes['img'],
                            'icon' => (string)$attributes['icon'],
                            'album_name' => (string)$attributes['album_name'],
                            'artist_name' => (string)$attributes['artist_name'],
                            'song_name' => (string)$attributes['song_name'],
                            'text' => (string)$attributes['text']
                        ];
                    } elseif ($childName === 'radio') {
                        $sectionData['items'][] = [
                            'type' => '电台',
                            'name' => (string)$attributes['name'],
                            'id' => (string)$attributes['id'],
                            'radio_id' => (string)$attributes['radio_id'],
                            'img' => (string)$attributes['img'],
                            'desc' => (string)$attributes['desc'],
                            'desctext' => (string)$attributes['desctext'],
                            'listencnt' => (string)$attributes['listencnt'],
                            'album_name' => (string)$attributes['album_name'],
                            'artist_name' => (string)$attributes['artist_name'],
                            'song_name' => (string)$attributes['song_name']
                        ];
                    } elseif ($childName === 'list') {
                        $sectionData['items'][] = [
                            'type' => '分类',
                            'name' => (string)$attributes['name'],
                            'id' => (string)$attributes['id'],
                            'img' => (string)$attributes['img']
                        ];
                    }
                }
                break;
                
            case 'caption_songlist':
                $sectionData['name'] = (string)$section['label'];
                $sectionData['psrc'] = (string)$section['psrc'];
                $sectionData['songs'] = [];
                
                // 解析歌曲列表
                foreach ($section->music as $music) {
                    $attributes = $music->attributes();
                    $song = [
                        'name' => (string)$attributes['name'],
                        'rid' => (string)$attributes['id'],
                        'artist' => (string)$attributes['artist'],
                        'artistid' => (string)$attributes['artistid'],
                        'album' => (string)$attributes['album'],
                        'albumid' => (string)$attributes['albumid'],
                        'duration' => (string)$attributes['duration'],
                        'img' => (string)$attributes['img'],
                        'vid' => (string)$attributes['vid'],
                        'mvquality' => (string)$attributes['mvquality'],
                        'minfo' => (string)$attributes['minfo'],
                        'free' => (string)$attributes['free'] === 'true',
                        'pay_flag' => (string)$attributes['pay_flag'],
                        'content_type' => (string)$attributes['content_type'],
                        'format' => (string)$attributes['format'],
                        'mvflag' => (string)$attributes['mvflag'],
                        'kmark' => (string)$attributes['kmark']
                    ];
                    
                    // 添加标签信息（如果有）
                    if (isset($attributes['tag_name'])) {
                        $song['tag_name'] = (string)$attributes['tag_name'];
                        $song['tag_type'] = (string)$attributes['tag_type'];
                    }
                    
                    // 添加榜单信息（如果有）
                    if (isset($attributes['tag_name_base'])) {
                        $song['tag_name_base'] = (string)$attributes['tag_name_base'];
                        $song['tag_name_rank'] = (string)$attributes['tag_name_rank'];
                    }
                    
                    $sectionData['songs'][] = $song;
                    $result['data']['songs'][] = $song;
                }
                break;
        }
        
        $result['data']['sections'][] = $sectionData;
    }
    
    // 统计分类信息
    $categories = [];
    foreach ($result['data']['sections'] as $section) {
        if ($section['type'] === 'caption_songlist') {
            $categories[] = [
                'name' => $section['name'],
                'psrc' => $section['psrc'] ?? '',
                'song_count' => count($section['songs'])
            ];
        } elseif ($section['type'] === 'big_business') {
            foreach ($section['items'] as $item) {
                $categories[] = [
                    'name' => $item['name'],
                    'type' => $item['type'],
                    'id' => $item['id'] ?? ''
                ];
            }
        }
    }
    
    $result['data']['categories'] = $categories;
    $result['data']['total_songs'] = count($result['data']['songs']);
    
    return $result;
}

// 主程序
try {
    $result = parse666File();
    
    if (!$result['success']) {
        throw new Exception($result['message']);
    }
    
    // 构建响应
    $response = [
        'code' => 200,
        'success' => true,
        'message' => '解析成功',
        'data' => $result['data'],
        'timestamp' => time()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(400);
    $errorResponse = [
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null,
        'timestamp' => time()
    ];
    
    echo json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
